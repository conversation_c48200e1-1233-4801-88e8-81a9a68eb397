#!/usr/bin/env python3
"""
RNNoise训练系统测试脚本
验证所有组件是否正常工作
"""

import os
import sys
import importlib
import subprocess
from pathlib import Path

def test_imports():
    """测试Python包导入"""
    print("=== 测试Python包导入 ===")
    
    required_modules = [
        ('torch', 'PyTorch'),
        ('numpy', 'NumPy'),
        ('tqdm', 'tqdm'),
    ]
    
    success = True
    for module_name, display_name in required_modules:
        try:
            importlib.import_module(module_name)
            print(f"✓ {display_name}")
        except ImportError:
            print(f"✗ {display_name} (请安装: pip install {module_name})")
            success = False
    
    return success

def test_local_modules():
    """测试本地模块导入"""
    print("\n=== 测试本地模块导入 ===")
    
    local_modules = [
        ('rnnoise', 'RNNoise模型'),
        ('sparsification', '稀疏化模块'),
    ]
    
    success = True
    for module_name, display_name in local_modules:
        try:
            importlib.import_module(module_name)
            print(f"✓ {display_name}")
        except ImportError as e:
            print(f"✗ {display_name}: {e}")
            success = False
    
    return success

def test_files():
    """测试必要文件是否存在"""
    print("\n=== 测试文件存在性 ===")
    
    required_files = [
        ('train_rnnoise.py', '原始训练脚本'),
        ('train_rnnoise_original.py', '训练包装脚本'),
        ('validate_model.py', '验证脚本'),
        ('run_complete_training.py', '完整流程脚本'),
        ('dump_rnnoise_weights.py', '权重导出脚本'),
        ('rnnoise.py', 'RNNoise模型定义'),
        ('sparsification/__init__.py', '稀疏化模块'),
        ('sparsification/common.py', '稀疏化通用函数'),
        ('sparsification/gru_sparsifier.py', 'GRU稀疏化器'),
    ]
    
    success = True
    for file_path, description in required_files:
        if os.path.exists(file_path):
            print(f"✓ {description}: {file_path}")
        else:
            print(f"✗ {description}: {file_path} (缺失)")
            success = False
    
    return success

def test_features():
    """测试特征文件"""
    print("\n=== 测试特征文件 ===")
    
    features_file = "features/original_training_features.f32"
    
    if os.path.exists(features_file):
        file_size = os.path.getsize(features_file)
        print(f"✓ 特征文件存在: {features_file}")
        print(f"  文件大小: {file_size / (1024*1024):.2f} MB")
        
        # 计算特征数量
        feature_count = file_size // (98 * 4)  # 98维特征，每个float32占4字节
        print(f"  特征帧数: {feature_count}")
        
        if feature_count > 1000:
            print("✓ 特征数量充足")
            return True
        else:
            print("⚠️ 特征数量较少，可能影响训练效果")
            return True
    else:
        print(f"✗ 特征文件不存在: {features_file}")
        print("  请运行: python extract_original_features.py")
        return False

def test_data_directories():
    """测试数据目录"""
    print("\n=== 测试数据目录 ===")
    
    directories = [
        ('clean_PCM_data', '清晰语音PCM数据'),
        ('noise_PCM_data', '噪声语音PCM数据'),
        ('test_voice', '测试音频'),
    ]
    
    for dir_path, description in directories:
        if os.path.exists(dir_path):
            files = os.listdir(dir_path)
            print(f"✓ {description}: {dir_path} ({len(files)} 个文件)")
        else:
            print(f"⚠️ {description}: {dir_path} (不存在)")

def test_gpu():
    """测试GPU可用性"""
    print("\n=== 测试GPU可用性 ===")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_device)
            print(f"✓ CUDA可用")
            print(f"  GPU数量: {gpu_count}")
            print(f"  当前GPU: {gpu_name}")
            print(f"  显存: {torch.cuda.get_device_properties(current_device).total_memory / (1024**3):.1f} GB")
            return True
        else:
            print("⚠️ CUDA不可用，将使用CPU训练")
            return True
    except Exception as e:
        print(f"✗ GPU测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n=== 测试模型创建 ===")
    
    try:
        import torch
        import rnnoise
        
        # 创建模型
        model = rnnoise.RNNoise(cond_size=128, gru_size=384)
        
        # 测试前向传播
        batch_size = 2
        seq_len = 100
        input_dim = 65
        
        test_input = torch.randn(batch_size, seq_len, input_dim)
        
        with torch.no_grad():
            gain, vad, states = model(test_input)
        
        print(f"✓ 模型创建成功")
        print(f"  输入形状: {test_input.shape}")
        print(f"  增益输出形状: {gain.shape}")
        print(f"  VAD输出形状: {vad.shape}")
        print(f"  模型参数数量: {sum(p.numel() for p in model.parameters())}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        return False

def test_training_script():
    """测试训练脚本语法"""
    print("\n=== 测试训练脚本语法 ===")
    
    scripts = [
        'train_rnnoise.py',
        'train_rnnoise_original.py',
        'validate_model.py',
        'run_complete_training.py',
    ]
    
    success = True
    for script in scripts:
        try:
            # 使用python -m py_compile检查语法
            result = subprocess.run([sys.executable, '-m', 'py_compile', script], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ {script}")
            else:
                print(f"✗ {script}: {result.stderr}")
                success = False
        except Exception as e:
            print(f"✗ {script}: {e}")
            success = False
    
    return success

def main():
    print("🔍 RNNoise训练系统测试")
    print("=" * 50)
    
    tests = [
        ("Python包导入", test_imports),
        ("本地模块导入", test_local_modules),
        ("文件存在性", test_files),
        ("特征文件", test_features),
        ("数据目录", test_data_directories),
        ("GPU可用性", test_gpu),
        ("模型创建", test_model_creation),
        ("脚本语法", test_training_script),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统准备就绪。")
        print("\n下一步:")
        print("  python run_complete_training.py --epochs 50  # 快速测试")
        print("  python run_complete_training.py --epochs 200 # 完整训练")
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，请检查并修复问题。")
        
        if not any(name == "特征文件" and result for name, result in results):
            print("\n💡 提示: 如果特征文件缺失，请先运行:")
            print("  python extract_original_features.py")

if __name__ == "__main__":
    main()
