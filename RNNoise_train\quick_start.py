#!/usr/bin/env python3
"""
RNNoise快速开始脚本
快速验证和启动训练
"""

import os
import sys
import subprocess

def check_basic_requirements():
    """检查基本要求"""
    print("🔍 检查基本要求...")
    
    # 检查Python包
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
    except ImportError:
        print("✗ PyTorch未安装")
        return False
    
    try:
        import numpy
        print(f"✓ NumPy {numpy.__version__}")
    except ImportError:
        print("✗ NumPy未安装")
        return False
    
    try:
        import tqdm
        print(f"✓ tqdm")
    except ImportError:
        print("✗ tqdm未安装")
        return False
    
    # 检查特征文件
    features_file = "features/original_training_features.f32"
    if os.path.exists(features_file):
        file_size = os.path.getsize(features_file)
        print(f"✓ 特征文件: {file_size / (1024*1024):.1f} MB")
    else:
        print(f"✗ 特征文件不存在: {features_file}")
        return False
    
    # 检查核心文件
    core_files = [
        "rnnoise.py",
        "train_rnnoise.py", 
        "train_rnnoise_original.py",
        "validate_model.py"
    ]
    
    for file in core_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file}")
            return False
    
    return True

def test_model_creation():
    """测试模型创建"""
    print("\n🧪 测试模型创建...")
    
    try:
        import torch
        sys.path.append('.')
        import rnnoise
        
        model = rnnoise.RNNoise(cond_size=128, gru_size=384)
        print(f"✓ 模型创建成功")
        print(f"✓ 参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试前向传播
        test_input = torch.randn(1, 100, 65)
        with torch.no_grad():
            gain, vad, states = model(test_input)
        print(f"✓ 前向传播测试通过")
        
        return True
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        return False

def run_quick_training():
    """运行快速训练测试"""
    print("\n🚀 开始快速训练测试 (10轮)...")
    
    cmd = [
        sys.executable, 'train_rnnoise_original.py',
        '--epochs', '10',
        '--batch-size', '32',
        '--suffix', '_quick_test'
    ]
    
    try:
        print("执行命令:", ' '.join(cmd))
        result = subprocess.run(cmd, check=True, cwd='.')
        print("✓ 快速训练测试完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 快速训练失败: {e}")
        return False

def run_validation():
    """运行验证"""
    print("\n🔍 运行模型验证...")
    
    # 查找最新的测试模型
    checkpoints_dir = "model_output/checkpoints"
    if not os.path.exists(checkpoints_dir):
        print("✗ 没有找到检查点目录")
        return False
    
    model_files = [f for f in os.listdir(checkpoints_dir) if f.endswith('_quick_test_10.pth')]
    if not model_files:
        print("✗ 没有找到测试模型")
        return False
    
    model_path = os.path.join(checkpoints_dir, model_files[0])
    
    cmd = [
        sys.executable, 'validate_model.py',
        '--model', model_path,
        '--skip-denoise-test'  # 跳过降噪测试以加快速度
    ]
    
    try:
        print("执行命令:", ' '.join(cmd))
        result = subprocess.run(cmd, check=True, cwd='.')
        print("✓ 验证完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 验证失败: {e}")
        return False

def main():
    print("🎯 RNNoise快速开始")
    print("=" * 50)
    
    # 检查基本要求
    if not check_basic_requirements():
        print("\n❌ 基本要求检查失败")
        print("\n💡 解决方案:")
        print("1. 安装依赖: pip install torch numpy tqdm")
        print("2. 确保特征文件存在")
        return
    
    # 测试模型创建
    if not test_model_creation():
        print("\n❌ 模型测试失败")
        return
    
    print("\n✅ 所有检查通过！")
    
    # 询问是否运行快速训练
    response = input("\n是否运行快速训练测试 (10轮)? [y/N]: ").strip().lower()
    
    if response in ['y', 'yes']:
        if run_quick_training():
            print("\n🎉 快速训练测试成功！")
            
            # 询问是否运行验证
            response = input("\n是否运行验证测试? [y/N]: ").strip().lower()
            if response in ['y', 'yes']:
                run_validation()
        else:
            print("\n❌ 快速训练测试失败")
    
    print("\n📋 下一步:")
    print("1. 完整训练: python run_complete_training.py --epochs 200")
    print("2. 快速训练: python run_complete_training.py --epochs 50")
    print("3. 查看文档: README_TRAINING.md")

if __name__ == "__main__":
    main()
