#!/usr/bin/env python3
"""
RNNoise模型验证脚本
包含降噪效果验证和格式转换检查
"""

import os
import sys
import argparse
import numpy as np
import torch
import struct
import wave
import subprocess
from pathlib import Path

# 导入RNNoise模型
import rnnoise

def load_model(model_path):
    """加载训练好的模型"""
    print(f"加载模型: {model_path}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # 创建模型
    model_args = checkpoint.get('model_args', ())
    model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 384})
    
    model = rnnoise.RNNoise(*model_args, **model_kwargs)
    model.load_state_dict(checkpoint['state_dict'])
    model.eval()
    
    print(f"模型参数: {sum(p.numel() for p in model.parameters())}")
    print(f"训练轮次: {checkpoint.get('epoch', 'Unknown')}")
    print(f"训练损失: {checkpoint.get('loss', 'Unknown')}")
    
    return model

def pcm_to_wav(pcm_file, wav_file, reference_wav=None, sample_rate=48000):
    """将PCM文件转换为WAV文件，如果提供参考WAV文件，则使用相同的参数"""
    try:
        # 读取PCM数据
        with open(pcm_file, 'rb') as f:
            pcm_data = f.read()

        # 如果提供了参考WAV文件，使用其参数
        if reference_wav and os.path.exists(reference_wav):
            with wave.open(reference_wav, 'rb') as ref_wav:
                channels = ref_wav.getnchannels()
                sampwidth = ref_wav.getsampwidth()
                framerate = ref_wav.getframerate()
                print(f"使用参考WAV参数: {channels}声道, {sampwidth*8}位, {framerate}Hz")
        else:
            # 使用默认参数
            channels = 1
            sampwidth = 2
            framerate = sample_rate

        # 写入WAV文件
        with wave.open(wav_file, 'wb') as wav:
            wav.setnchannels(channels)
            wav.setsampwidth(sampwidth)
            wav.setframerate(framerate)
            wav.writeframes(pcm_data)

        print(f"✓ PCM转WAV成功: {wav_file}")
        return True

    except Exception as e:
        print(f"✗ PCM转WAV失败: {e}")
        return False

def wav_to_pcm(wav_file, pcm_file):
    """将WAV文件转换为PCM文件"""
    try:
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            
        with open(pcm_file, 'wb') as f:
            f.write(frames)
        
        print(f"✓ WAV转PCM成功: {pcm_file}")
        return True
        
    except Exception as e:
        print(f"✗ WAV转PCM失败: {e}")
        return False

def extract_training_features_from_pcm(pcm_file):
    """直接从PCM文件提取98维训练特征（与训练时完全一致）"""
    try:
        # 读取PCM数据
        with open(pcm_file, 'rb') as f:
            data = f.read()

        # 转换为float32数组
        samples = struct.unpack(f'<{len(data)//2}h', data)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0

        frame_size = 480  # RNNoise标准帧大小
        num_frames = len(audio_data) // frame_size

        if num_frames < 1:
            print("✗ 音频太短，无法提取特征")
            return None

        print(f"开始提取训练特征，音频长度: {len(audio_data)} 样本，帧数: {num_frames}")

        features = []

        for i in range(num_frames):
            start_idx = i * frame_size
            end_idx = start_idx + frame_size

            if end_idx > len(audio_data):
                # 最后一帧，用零填充
                frame = np.zeros(frame_size, dtype=np.float32)
                frame[:len(audio_data) - start_idx] = audio_data[start_idx:]
            else:
                frame = audio_data[start_idx:end_idx]

            # 提取完整的98维特征
            frame_features = extract_complete_frame_features(frame)
            if frame_features is not None:
                features.append(frame_features)

        if features:
            features_array = np.array(features, dtype=np.float32)
            print(f"✓ 提取特征成功: {features_array.shape}")
            return features_array
        else:
            print("✗ 特征提取失败")
            return None

    except Exception as e:
        print(f"训练特征提取失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def extract_complete_frame_features(frame):
    """提取单帧的完整98维特征（输入65维 + 目标33维）"""
    try:
        frame_size = len(frame)

        # === 输入特征 (65维) ===

        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])  # 只取前256个bin
        fft_power = fft_magnitude ** 2

        # 1. Bark频谱 (22维)
        bark_bands = 22
        bark_spectrum = np.zeros(bark_bands)
        for b in range(bark_bands):
            start_bin = int(b * 256 / bark_bands)
            end_bin = int((b + 1) * 256 / bark_bands)
            bark_spectrum[b] = np.mean(fft_magnitude[start_bin:end_bin])

        # 2. MFCC特征 (13维)
        mel_filters = 13
        mfcc_features = np.zeros(mel_filters)
        for m in range(mel_filters):
            start_bin = int(m * 128 / mel_filters)
            end_bin = int((m + 1) * 128 / mel_filters)
            mel_energy = np.sum(fft_power[start_bin:end_bin])
            mfcc_features[m] = np.log(mel_energy + 1e-10)

        # 3. 基音特征 (6维)
        pitch_features = np.zeros(6)
        autocorr = np.correlate(frame, frame, mode='full')
        autocorr = autocorr[len(autocorr)//2:]

        min_period = 20
        max_period = 320

        if len(autocorr) > max_period:
            pitch_corr = autocorr[min_period:max_period]
            if len(pitch_corr) > 0:
                pitch_period = np.argmax(pitch_corr) + min_period
                pitch_gain = pitch_corr[np.argmax(pitch_corr)] / (autocorr[0] + 1e-10)

                pitch_features[0] = pitch_period / 320.0
                pitch_features[1] = np.clip(pitch_gain, 0, 1)
                pitch_features[2] = np.std(frame)
                pitch_features[3] = np.mean(np.abs(frame))
                pitch_features[4] = np.sum(frame ** 2) / frame_size
                pitch_features[5] = np.max(np.abs(frame))

        # 4. 频谱特征 (24维)
        spectral_features = np.zeros(24)
        freqs = np.arange(256) * 48000 / 512

        # 频谱质心
        spectral_centroid = np.sum(freqs * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10)
        spectral_features[0] = spectral_centroid / 24000.0

        # 频谱带宽
        spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10))
        spectral_features[1] = spectral_bandwidth / 12000.0

        # 频谱滚降
        cumsum_magnitude = np.cumsum(fft_magnitude)
        rolloff_threshold = 0.85 * cumsum_magnitude[-1]
        rolloff_idx = np.where(cumsum_magnitude >= rolloff_threshold)[0]
        if len(rolloff_idx) > 0:
            spectral_features[2] = rolloff_idx[0] / 256.0

        # 频谱平坦度
        geometric_mean = np.exp(np.mean(np.log(fft_magnitude + 1e-10)))
        arithmetic_mean = np.mean(fft_magnitude)
        spectral_features[3] = geometric_mean / (arithmetic_mean + 1e-10)

        # 频带能量 (20维)
        for j in range(20):
            start_bin = int(j * 256 / 20)
            end_bin = int((j + 1) * 256 / 20)
            spectral_features[4 + j] = np.mean(fft_magnitude[start_bin:end_bin])

        # 组合输入特征 (65维)
        input_features = np.concatenate([
            bark_spectrum,      # 22维
            mfcc_features,      # 13维
            pitch_features,     # 6维
            spectral_features   # 24维
        ])

        # === 目标特征 (33维) ===

        # 5. 目标增益 (32维) - 简化版本，实际训练时会有真实的目标
        target_gain = np.ones(32) * 0.8  # 默认增益

        # 6. 目标VAD (1维) - 简化版本
        # 基于能量的简单VAD
        frame_energy = np.sum(frame ** 2)
        target_vad = np.array([1.0 if frame_energy > 0.001 else 0.0])

        # 组合完整特征 (98维)
        complete_features = np.concatenate([
            input_features,     # 65维
            target_gain,        # 32维
            target_vad          # 1维
        ])

        return complete_features.astype(np.float32)

    except Exception as e:
        print(f"单帧特征提取失败: {e}")
        return None

def denoise_audio_with_training_features(model, pcm_file, output_pcm_file):
    """使用RNNoise模型和训练特征对音频进行降噪"""
    try:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        model.eval()

        print(f"开始使用训练特征进行降噪...")

        # 1. 提取完整的训练特征 (98维)
        features = extract_training_features_from_pcm(pcm_file)
        if features is None:
            print("✗ 特征提取失败")
            return False

        print(f"✓ 提取特征成功: {features.shape}")

        # 2. 读取原始音频数据
        with open(pcm_file, 'rb') as f:
            data = f.read()

        if len(data) % 2 != 0:
            data = data[:-1]

        samples = struct.unpack(f'<{len(data)//2}h', data)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0

        # 3. 使用RNNoise模型处理特征
        # 分离输入特征 (前65维) 和目标 (后33维)
        input_features = features[:, :65]  # 输入特征
        target_gain = features[:, 65:97]   # 目标增益 (32维)
        target_vad = features[:, 97:98]    # 目标VAD (1维)

        print(f"输入特征形状: {input_features.shape}")
        print(f"目标增益形状: {target_gain.shape}")
        print(f"目标VAD形状: {target_vad.shape}")

        # 转换为tensor
        input_tensor = torch.from_numpy(input_features).unsqueeze(0).to(device)  # [1, seq_len, 65]

        with torch.no_grad():
            # RNNoise前向传播
            pred_gain, pred_vad, _ = model(input_tensor)

            # 获取预测结果
            predicted_gain = pred_gain.squeeze(0).cpu().numpy()  # [seq_len, 32]
            predicted_vad = pred_vad.squeeze(0).cpu().numpy()    # [seq_len, 1]

        print(f"✓ RNNoise推理完成")
        print(f"预测增益形状: {predicted_gain.shape}")
        print(f"预测VAD形状: {predicted_vad.shape}")

        # 4. 应用降噪到音频
        frame_size = 480  # RNNoise标准帧大小
        denoised_audio = np.copy(audio_data)

        num_audio_frames = len(audio_data) // frame_size
        num_feature_frames = len(predicted_gain)

        print(f"音频帧数: {num_audio_frames}, 特征帧数: {num_feature_frames}")

        # 处理音频帧
        frames_to_process = min(num_audio_frames, num_feature_frames)

        for i in range(frames_to_process):
            start_idx = i * frame_size
            end_idx = start_idx + frame_size

            if end_idx > len(audio_data):
                end_idx = len(audio_data)

            frame = audio_data[start_idx:end_idx]
            gain = predicted_gain[i]
            vad_score = predicted_vad[i][0]

            # 应用频域降噪
            denoised_frame = apply_spectral_denoising(frame, gain, vad_score)
            denoised_audio[start_idx:end_idx] = denoised_frame

        # 5. 保存降噪后的音频
        denoised_samples = np.clip(denoised_audio * 32768.0, -32768, 32767).astype(np.int16)

        with open(output_pcm_file, 'wb') as f:
            f.write(denoised_samples.tobytes())

        print(f"✓ 降噪音频保存: {output_pcm_file}")
        print(f"✓ 输出长度: {len(denoised_samples)} 样本 (与输入一致)")
        return True

    except Exception as e:
        print(f"✗ 降噪处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def extract_frame_features(frame, frame_size=480):
    """为单个音频帧提取65维特征"""
    try:
        # 确保帧长度正确
        if len(frame) < frame_size:
            padded_frame = np.zeros(frame_size)
            padded_frame[:len(frame)] = frame
            frame = padded_frame
        elif len(frame) > frame_size:
            frame = frame[:frame_size]

        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])  # 只取前256个bin

        # Bark频谱 (22维)
        bark_bands = 22
        bark_spectrum = np.zeros(bark_bands)
        for b in range(bark_bands):
            start_bin = int(b * 256 / bark_bands)
            end_bin = int((b + 1) * 256 / bark_bands)
            bark_spectrum[b] = np.mean(fft_magnitude[start_bin:end_bin])

        # MFCC特征 (13维)
        mel_filters = 13
        mfcc_features = np.zeros(mel_filters)
        for m in range(mel_filters):
            start_bin = int(m * 128 / mel_filters)
            end_bin = int((m + 1) * 128 / mel_filters)
            mel_energy = np.sum(fft_magnitude[start_bin:end_bin] ** 2)
            mfcc_features[m] = np.log(mel_energy + 1e-10)

        # 基音特征 (6维)
        pitch_features = np.zeros(6)
        autocorr = np.correlate(frame, frame, mode='full')
        autocorr = autocorr[len(autocorr)//2:]

        min_period = 20
        max_period = 320

        if len(autocorr) > max_period:
            pitch_corr = autocorr[min_period:max_period]
            if len(pitch_corr) > 0:
                pitch_period = np.argmax(pitch_corr) + min_period
                pitch_gain = pitch_corr[np.argmax(pitch_corr)] / (autocorr[0] + 1e-10)

                pitch_features[0] = pitch_period / 320.0
                pitch_features[1] = pitch_gain
                pitch_features[2] = np.std(frame)
                pitch_features[3] = np.mean(np.abs(frame))
                pitch_features[4] = np.sum(frame ** 2)
                pitch_features[5] = np.max(np.abs(frame))

        # 频谱特征 (24维)
        spectral_features = np.zeros(24)
        freqs = np.arange(256) * 48000 / 512
        spectral_centroid = np.sum(freqs * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10)
        spectral_features[0] = spectral_centroid / 24000.0

        spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10))
        spectral_features[1] = spectral_bandwidth / 12000.0

        cumsum_magnitude = np.cumsum(fft_magnitude)
        rolloff_threshold = 0.85 * cumsum_magnitude[-1]
        rolloff_idx = np.where(cumsum_magnitude >= rolloff_threshold)[0]
        if len(rolloff_idx) > 0:
            spectral_features[2] = rolloff_idx[0] / 256.0

        geometric_mean = np.exp(np.mean(np.log(fft_magnitude + 1e-10)))
        arithmetic_mean = np.mean(fft_magnitude)
        spectral_features[3] = geometric_mean / (arithmetic_mean + 1e-10)

        for j in range(20):
            start_bin = int(j * 256 / 20)
            end_bin = int((j + 1) * 256 / 20)
            spectral_features[4 + j] = np.mean(fft_magnitude[start_bin:end_bin])

        # 组合输入特征 (65维)
        input_features = np.concatenate([
            bark_spectrum,      # 22维
            mfcc_features,      # 13维
            pitch_features,     # 6维
            spectral_features   # 24维
        ])

        return input_features.astype(np.float32)

    except Exception as e:
        print(f"特征提取失败: {e}")
        return None

def apply_spectral_denoising(frame, gain, vad_score, frame_size=480):
    """应用频谱降噪"""
    try:
        # 对音频帧进行FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame)
        fft_phase = np.angle(fft_frame)

        # 应用32频带增益
        denoised_magnitude = np.copy(fft_magnitude)

        for band in range(32):
            # 计算每个频带对应的FFT bin范围
            start_bin = int(band * 256 / 32)
            end_bin = int((band + 1) * 256 / 32)

            # 获取该频带的增益
            band_gain = gain[band]

            # 根据VAD调整增益应用策略
            if vad_score > 0.5:  # 语音帧
                # 对语音帧使用更保守的增益
                applied_gain = 0.7 + 0.3 * np.clip(band_gain, 0.3, 1.5)
            else:  # 噪声帧
                # 对噪声帧使用更激进的降噪
                applied_gain = np.clip(band_gain, 0.1, 1.0)

            # 应用增益
            denoised_magnitude[start_bin:end_bin] *= applied_gain
            # 处理对称部分
            if start_bin > 0:
                denoised_magnitude[512-end_bin:512-start_bin] *= applied_gain

        # 重构复数频谱
        denoised_fft = denoised_magnitude * np.exp(1j * fft_phase)

        # IFFT回时域
        denoised_frame = np.real(np.fft.ifft(denoised_fft))[:frame_size]

        # 限制幅度
        denoised_frame = np.clip(denoised_frame, -1.0, 1.0)

        return denoised_frame

    except Exception as e:
        print(f"频谱降噪失败: {e}")
        return frame

# 旧的apply_gain_to_audio函数已删除，使用denoise_audio_with_training_features代替

def test_format_conversion():
    """测试格式转换功能"""
    print("\n=== 测试格式转换 ===")
    
    # 创建测试目录
    test_dir = "test_conversion"
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成测试音频 (1秒，1000Hz正弦波)
    sample_rate = 48000
    duration = 1.0
    frequency = 1000.0
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    test_audio = np.sin(2 * np.pi * frequency * t) * 0.5
    test_samples = (test_audio * 32767).astype(np.int16)
    
    # 保存为PCM
    test_pcm = os.path.join(test_dir, "test_original.pcm")
    with open(test_pcm, 'wb') as f:
        f.write(test_samples.tobytes())
    
    # PCM转WAV
    test_wav = os.path.join(test_dir, "test_converted.wav")
    pcm_success = pcm_to_wav(test_pcm, test_wav, sample_rate)
    
    # WAV转PCM
    test_pcm2 = os.path.join(test_dir, "test_reconverted.pcm")
    wav_success = wav_to_pcm(test_wav, test_pcm2)
    
    # 验证数据一致性
    if pcm_success and wav_success:
        with open(test_pcm, 'rb') as f:
            original_data = f.read()
        with open(test_pcm2, 'rb') as f:
            converted_data = f.read()
        
        if original_data == converted_data:
            print("✓ 格式转换测试通过 - 数据完全一致")
        else:
            print("⚠️ 格式转换测试警告 - 数据略有差异")
    
    return pcm_success and wav_success

def test_denoising_effect(model, test_audio_dir="test_voice"):
    """测试降噪效果"""
    print("\n=== 测试降噪效果 ===")

    if not os.path.exists(test_audio_dir):
        print(f"测试音频目录不存在: {test_audio_dir}")
        return False

    # 获取测试音频文件
    audio_files = [f for f in os.listdir(test_audio_dir) if f.endswith('.wav')]
    if not audio_files:
        print("没有找到测试音频文件")
        return False

    # 创建输出目录
    output_dir = "denoised_output"
    os.makedirs(output_dir, exist_ok=True)

    success_count = 0

    for audio_file in audio_files[:3]:  # 测试前3个文件
        print(f"\n处理音频: {audio_file}")

        wav_path = os.path.join(test_audio_dir, audio_file)
        base_name = os.path.splitext(audio_file)[0]

        # === 步骤1: 验证原始音频的两次格式转换还原能力 ===
        print(f"--- 验证原始音频格式转换还原 ---")

        # 原始WAV → PCM
        original_pcm = os.path.join(output_dir, f"{base_name}_original.pcm")
        if not wav_to_pcm(wav_path, original_pcm):
            print(f"✗ 原始音频WAV→PCM转换失败")
            continue

        # PCM → WAV (还原)
        restored_original_wav = os.path.join(output_dir, f"{base_name}_original_restored.wav")
        if not pcm_to_wav(original_pcm, restored_original_wav, reference_wav=wav_path):
            print(f"✗ 原始音频PCM→WAV还原失败")
            continue

        print(f"✓ 原始音频两次格式转换还原成功: {restored_original_wav}")

        # === 步骤2: 对PCM数据进行RNNoise降噪处理 ===
        print(f"--- 进行RNNoise降噪处理 ---")

        denoised_pcm = os.path.join(output_dir, f"{base_name}_denoised.pcm")
        if not denoise_audio_with_training_features(model, original_pcm, denoised_pcm):
            print(f"✗ RNNoise降噪处理失败")
            continue

        # === 步骤3: 降噪后的PCM数据转换回WAV ===
        print(f"--- 降噪数据格式转换还原 ---")

        denoised_wav = os.path.join(output_dir, f"{base_name}_denoised.wav")
        if not pcm_to_wav(denoised_pcm, denoised_wav, reference_wav=wav_path):
            print(f"✗ 降噪音频PCM→WAV还原失败")
            continue

        print(f"✓ 降噪音频格式转换还原成功: {denoised_wav}")

        # === 步骤4: 保存原始音频副本用于对比 ===
        original_copy = os.path.join(output_dir, f"{base_name}_original.wav")
        import shutil
        shutil.copy2(wav_path, original_copy)
        print(f"✓ 原始音频副本: {original_copy}")

        # === 验证文件大小和基本信息 ===
        try:
            import wave

            # 检查原始文件
            with wave.open(wav_path, 'rb') as orig:
                orig_frames = orig.getnframes()
                orig_rate = orig.getframerate()
                orig_channels = orig.getnchannels()

            # 检查还原的原始文件
            with wave.open(restored_original_wav, 'rb') as restored:
                restored_frames = restored.getnframes()
                restored_rate = restored.getframerate()
                restored_channels = restored.getnchannels()

            # 检查降噪文件
            with wave.open(denoised_wav, 'rb') as denoised:
                denoised_frames = denoised.getnframes()
                denoised_rate = denoised.getframerate()
                denoised_channels = denoised.getnchannels()

            print(f"文件信息验证:")
            print(f"  原始: {orig_frames}帧, {orig_rate}Hz, {orig_channels}声道")
            print(f"  还原: {restored_frames}帧, {restored_rate}Hz, {restored_channels}声道")
            print(f"  降噪: {denoised_frames}帧, {denoised_rate}Hz, {denoised_channels}声道")

            # 验证一致性
            if (orig_frames == restored_frames == denoised_frames and
                orig_rate == restored_rate == denoised_rate and
                orig_channels == restored_channels == denoised_channels):
                print(f"✓ 所有文件格式参数完全一致")
                success_count += 1
            else:
                print(f"⚠️ 文件格式参数不一致")

        except Exception as e:
            print(f"⚠️ 文件信息验证失败: {e}")

    print(f"\n降噪测试完成: {success_count}/{len(audio_files[:3])} 成功")
    print(f"\n输出文件说明:")
    print(f"  *_original.wav - 原始音频副本")
    print(f"  *_original_restored.wav - 原始音频经过两次格式转换后的还原版本")
    print(f"  *_denoised.wav - 经过RNNoise降噪处理的音频")
    print(f"\n验证要点:")
    print(f"  1. 原始音频两次格式转换是否能完美还原")
    print(f"  2. 降噪音频是否保持相同的格式参数")
    print(f"  3. 降噪效果是否明显（需要人工听音验证）")

    return success_count > 0

def main():
    parser = argparse.ArgumentParser(description='RNNoise模型验证脚本')

    parser.add_argument('--model', type=str, required=True,
                       help='训练好的模型文件路径 (.pth)')
    parser.add_argument('--test-audio-dir', type=str, default='test_voice',
                       help='测试音频目录 (默认: test_voice)')
    parser.add_argument('--skip-format-test', action='store_true',
                       help='跳过格式转换测试')
    parser.add_argument('--skip-denoise-test', action='store_true',
                       help='跳过降噪效果测试')

    args = parser.parse_args()

    try:
        print("=== RNNoise模型验证 ===")

        # 加载模型
        model = load_model(args.model)

        # 测试格式转换
        if not args.skip_format_test:
            format_test_success = test_format_conversion()
            if not format_test_success:
                print("⚠️ 格式转换测试失败")

        # 测试降噪效果
        if not args.skip_denoise_test:
            denoise_test_success = test_denoising_effect(model, args.test_audio_dir)
            if not denoise_test_success:
                print("⚠️ 降噪效果测试失败")

        print("\n=== 验证完成 ===")
        print("请检查以下文件:")
        print("1. test_conversion/ - 格式转换测试结果")
        print("2. denoised_output/ - 降噪效果测试结果")

    except Exception as e:
        print(f"❌ 验证失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
