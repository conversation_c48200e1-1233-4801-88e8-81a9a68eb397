#!/usr/bin/env python3
"""
RNNoise降噪效果验证脚本
基于训练代码的完整验证流程，不简化任何过程
"""

import numpy as np
import torch
from torch import nn
import torch.nn.functional as F
import os
import struct
import wave
import argparse
import rnnoise

def pcm_to_wav(pcm_file, wav_file, reference_wav=None, sample_rate=48000):
    """将PCM文件转换为WAV文件，使用参考WAV文件的参数"""
    try:
        with open(pcm_file, 'rb') as f:
            pcm_data = f.read()
        
        if reference_wav and os.path.exists(reference_wav):
            with wave.open(reference_wav, 'rb') as ref_wav:
                channels = ref_wav.getnchannels()
                sampwidth = ref_wav.getsampwidth()
                framerate = ref_wav.getframerate()
        else:
            channels = 1
            sampwidth = 2
            framerate = sample_rate
        
        with wave.open(wav_file, 'wb') as wav:
            wav.setnchannels(channels)
            wav.setsampwidth(sampwidth)
            wav.setframerate(framerate)
            wav.writeframes(pcm_data)
        
        return True
    except Exception as e:
        print(f"PCM转WAV失败: {e}")
        return False

def wav_to_pcm(wav_file, pcm_file):
    """将WAV文件转换为PCM文件"""
    try:
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            
        with open(pcm_file, 'wb') as f:
            f.write(frames)
        
        return True
    except Exception as e:
        print(f"WAV转PCM失败: {e}")
        return False

class RNNoiseTestDataset(torch.utils.data.Dataset):
    """基于训练代码的测试数据集类"""
    def __init__(self, features_file, sequence_length=2000):
        self.sequence_length = sequence_length
        
        # 使用与训练完全相同的数据加载方式
        self.data = np.memmap(features_file, dtype='float32', mode='r')
        dim = 98
        
        self.nb_sequences = self.data.shape[0] // self.sequence_length // dim
        self.data = self.data[:self.nb_sequences * self.sequence_length * dim]
        
        self.data = np.reshape(self.data, (self.nb_sequences, self.sequence_length, dim))
        
        print(f"测试数据集加载完成:")
        print(f"  序列数量: {self.nb_sequences}")
        print(f"  序列长度: {self.sequence_length}")
        print(f"  特征维度: {dim}")
    
    def __len__(self):
        return self.nb_sequences
    
    def __getitem__(self, index):
        # 与训练时完全相同的数据分割方式
        return (self.data[index, :, :65].copy(),      # 输入特征 (65维)
                self.data[index, :, 65:-1].copy(),    # 目标增益 (32维)
                self.data[index, :, -1:].copy())      # 目标VAD (1维)

def mask(g):
    """与训练时完全相同的mask函数"""
    return torch.clamp(g + 1, max=1)

def load_model(model_path, device):
    """加载训练好的模型"""
    print(f"加载模型: {model_path}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # 创建模型 - 使用与训练时完全相同的参数
    model_args = checkpoint.get('model_args', ())
    model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 384})
    
    model = rnnoise.RNNoise(*model_args, **model_kwargs)
    model.load_state_dict(checkpoint['state_dict'])
    model.to(device)
    model.eval()
    
    print(f"✓ 模型加载成功")
    print(f"  参数数量: {sum(p.numel() for p in model.parameters())}")
    print(f"  训练轮次: {checkpoint.get('epoch', 'Unknown')}")
    print(f"  训练损失: {checkpoint.get('loss', 'Unknown')}")
    
    return model

def extract_features_from_audio(wav_file):
    """从WAV音频文件提取特征，实时生成特征文件"""
    try:
        print(f"开始提取特征: {wav_file}")

        import tempfile
        import subprocess

        # 创建临时特征文件
        with tempfile.NamedTemporaryFile(suffix='.f32', delete=False) as temp_features:
            temp_features_path = temp_features.name

        try:
            # 调用特征提取脚本
            cmd = [
                'python', 'extract_original_features.py',
                '--input', wav_file,
                '--output', temp_features_path
            ]

            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd='.', timeout=60)

            if result.returncode == 0:
                print(f"✓ 特征提取成功: {temp_features_path}")
                # 检查文件大小
                if os.path.exists(temp_features_path):
                    file_size = os.path.getsize(temp_features_path)
                    print(f"  特征文件大小: {file_size} 字节")
                    if file_size > 0:
                        return temp_features_path
                    else:
                        print("✗ 特征文件为空")
                        return None
                else:
                    print("✗ 特征文件未生成")
                    return None
            else:
                print(f"✗ 特征提取失败:")
                print(f"  返回码: {result.returncode}")
                if result.stdout:
                    print(f"  stdout: {result.stdout}")
                if result.stderr:
                    print(f"  stderr: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            print("✗ 特征提取超时")
            return None
        except Exception as e:
            print(f"✗ 特征提取执行失败: {e}")
            return None

    except Exception as e:
        print(f"✗ 特征提取初始化失败: {e}")
        return None

def denoise_with_rnnoise(model, features_file, pcm_file, output_pcm_file, device, gamma=0.25):
    """使用RNNoise模型进行降噪，完全基于训练代码的处理方式"""
    try:
        print("开始RNNoise降噪处理...")
        
        # 1. 加载测试数据集 - 使用与训练完全相同的方式
        test_dataset = RNNoiseTestDataset(features_file, sequence_length=2000)
        
        if len(test_dataset) == 0:
            print("✗ 测试数据集为空")
            return False
        
        # 2. 读取原始音频数据
        with open(pcm_file, 'rb') as f:
            data = f.read()
        
        if len(data) % 2 != 0:
            data = data[:-1]
        
        samples = struct.unpack(f'<{len(data)//2}h', data)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0
        
        print(f"原始音频长度: {len(audio_data)} 样本")
        
        # 3. 使用模型进行推理 - 与训练时完全相同的前向传播
        denoised_audio = np.copy(audio_data)
        frame_size = 480  # RNNoise标准帧大小
        
        with torch.no_grad():
            # 处理每个序列
            for seq_idx in range(len(test_dataset)):
                features, _, _ = test_dataset[seq_idx]
                
                # 转换为tensor - 与训练时相同的格式
                features_tensor = torch.from_numpy(features).unsqueeze(0).to(device)  # [1, seq_len, 65]
                
                # RNNoise前向传播 - 与训练时完全相同
                pred_gain, pred_vad, _ = model(features_tensor)
                
                # 获取预测结果
                pred_gain = pred_gain.squeeze(0).cpu().numpy()  # [seq_len, 32]
                pred_vad = pred_vad.squeeze(0).cpu().numpy()    # [seq_len, 1]
                
                # 应用与训练时相同的处理
                # 注意：训练时使用 gain[:,3:-1,:] 和 vad[:,3:-1,:]
                # 所以我们也要相应地处理
                if pred_gain.shape[0] > 6:  # 确保有足够的帧数
                    processed_gain = pred_gain[3:-1, :]  # 去掉前3帧和后1帧
                    processed_vad = pred_vad[3:-1, :]
                else:
                    processed_gain = pred_gain
                    processed_vad = pred_vad
                
                # 应用降噪到对应的音频段
                start_audio_idx = seq_idx * 2000 * frame_size
                end_audio_idx = min(start_audio_idx + len(processed_gain) * frame_size, len(audio_data))
                
                if start_audio_idx < len(audio_data):
                    # 逐帧应用降噪
                    for frame_idx in range(len(processed_gain)):
                        audio_start = start_audio_idx + frame_idx * frame_size
                        audio_end = min(audio_start + frame_size, end_audio_idx)
                        
                        if audio_start < len(audio_data) and audio_end <= len(audio_data):
                            frame = audio_data[audio_start:audio_end]
                            gain = processed_gain[frame_idx]
                            vad_score = processed_vad[frame_idx][0]
                            
                            # 应用频域降噪
                            denoised_frame = apply_spectral_denoising(frame, gain, vad_score, gamma)
                            denoised_audio[audio_start:audio_end] = denoised_frame
        
        # 4. 保存降噪后的音频
        denoised_samples = np.clip(denoised_audio * 32768.0, -32768, 32767).astype(np.int16)
        
        with open(output_pcm_file, 'wb') as f:
            f.write(denoised_samples.tobytes())
        
        print(f"✓ 降噪处理完成: {output_pcm_file}")
        print(f"✓ 输出长度: {len(denoised_samples)} 样本")
        return True
        
    except Exception as e:
        print(f"✗ 降噪处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def apply_spectral_denoising(frame, gain, vad_score, gamma=0.25):
    """应用频谱降噪，基于训练时的增益处理方式"""
    try:
        if len(frame) == 0:
            return frame
        
        # 确保帧长度
        frame_size = 480
        if len(frame) < frame_size:
            padded_frame = np.zeros(frame_size)
            padded_frame[:len(frame)] = frame
            frame = padded_frame
        elif len(frame) > frame_size:
            frame = frame[:frame_size]
        
        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame)
        fft_phase = np.angle(fft_frame)
        
        # 应用32频带增益 - 与训练时的目标增益对应
        denoised_magnitude = np.copy(fft_magnitude)
        
        for band in range(32):
            # 计算每个频带对应的FFT bin范围
            start_bin = int(band * 256 / 32)
            end_bin = int((band + 1) * 256 / 32)
            
            # 获取该频带的增益
            band_gain = gain[band]
            
            # 应用与训练时相同的增益处理
            # 训练时使用: target_gain = torch.clamp(gain, min=0)
            # 然后: target_gain = target_gain*(torch.tanh(8*target_gain)**2)
            # 使用gamma参数进行感知加权
            processed_gain = max(0, band_gain)
            processed_gain = processed_gain * (np.tanh(8 * processed_gain) ** 2)
            # 应用gamma感知加权
            processed_gain = processed_gain ** gamma
            
            # 根据VAD调整增益应用
            if vad_score > 0.5:  # 语音帧
                applied_gain = 0.8 + 0.2 * processed_gain
            else:  # 噪声帧
                applied_gain = processed_gain
            
            # 限制增益范围
            applied_gain = np.clip(applied_gain, 0.1, 1.5)
            
            # 应用增益
            denoised_magnitude[start_bin:end_bin] *= applied_gain
            # 处理对称部分
            if start_bin > 0:
                denoised_magnitude[512-end_bin:512-start_bin] *= applied_gain
        
        # 重构复数频谱
        denoised_fft = denoised_magnitude * np.exp(1j * fft_phase)
        
        # IFFT回时域
        denoised_frame = np.real(np.fft.ifft(denoised_fft))[:frame_size]
        
        # 限制幅度
        denoised_frame = np.clip(denoised_frame, -1.0, 1.0)
        
        return denoised_frame[:len(frame)]

    except Exception as e:
        print(f"频谱降噪失败: {e}")
        return frame

def test_audio_denoising(model_path, test_audio_dir, output_dir="test_output"):
    """测试音频降噪效果"""
    print("=== RNNoise降噪效果验证 ===")
    print(f"模型路径: {model_path}")
    print(f"测试音频目录: {test_audio_dir}")
    print(f"输出目录: {output_dir}")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 设备选择
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 加载模型
    model = load_model(model_path, device)

    # 获取测试音频文件
    if not os.path.exists(test_audio_dir):
        print(f"✗ 测试音频目录不存在: {test_audio_dir}")
        return False

    audio_files = [f for f in os.listdir(test_audio_dir) if f.endswith('.wav')]
    if not audio_files:
        print("✗ 没有找到WAV音频文件")
        return False

    print(f"找到 {len(audio_files)} 个音频文件")

    success_count = 0

    for audio_file in audio_files:
        print(f"\n--- 处理音频: {audio_file} ---")

        wav_path = os.path.join(test_audio_dir, audio_file)
        base_name = os.path.splitext(audio_file)[0]

        try:
            # 步骤1: WAV转PCM
            pcm_path = os.path.join(output_dir, f"{base_name}.pcm")
            if not wav_to_pcm(wav_path, pcm_path):
                print(f"✗ WAV转PCM失败: {audio_file}")
                continue

            # 步骤2: 提取特征 (直接从WAV文件)
            features_file = extract_features_from_audio(wav_path)
            if features_file is None:
                print(f"✗ 特征提取失败: {audio_file}")
                continue

            # 步骤3: RNNoise降噪
            denoised_pcm = os.path.join(output_dir, f"{base_name}_denoised.pcm")
            if not denoise_with_rnnoise(model, features_file, pcm_path, denoised_pcm, device):
                print(f"✗ 降噪处理失败: {audio_file}")
                # 清理临时特征文件
                if os.path.exists(features_file):
                    os.unlink(features_file)
                continue

            # 步骤4: PCM转WAV (降噪后)
            denoised_wav = os.path.join(output_dir, f"{base_name}_denoised.wav")
            if not pcm_to_wav(denoised_pcm, denoised_wav, reference_wav=wav_path):
                print(f"✗ 降噪音频PCM转WAV失败: {audio_file}")
                # 清理临时文件
                if os.path.exists(features_file):
                    os.unlink(features_file)
                continue

            # 步骤5: 保存原始音频副本用于对比
            original_wav = os.path.join(output_dir, f"{base_name}_original.wav")
            import shutil
            shutil.copy2(wav_path, original_wav)

            # 步骤6: 验证文件完整性
            try:
                with wave.open(wav_path, 'rb') as orig:
                    orig_frames = orig.getnframes()
                    orig_rate = orig.getframerate()
                    orig_channels = orig.getnchannels()

                with wave.open(denoised_wav, 'rb') as denoised:
                    denoised_frames = denoised.getnframes()
                    denoised_rate = denoised.getframerate()
                    denoised_channels = denoised.getnchannels()

                print(f"文件验证:")
                print(f"  原始: {orig_frames}帧, {orig_rate}Hz, {orig_channels}声道")
                print(f"  降噪: {denoised_frames}帧, {denoised_rate}Hz, {denoised_channels}声道")

                if (orig_frames == denoised_frames and
                    orig_rate == denoised_rate and
                    orig_channels == denoised_channels):
                    print(f"✓ 文件格式验证通过")
                    success_count += 1
                else:
                    print(f"⚠️ 文件格式不一致")

            except Exception as e:
                print(f"⚠️ 文件验证失败: {e}")

            # 清理临时特征文件
            if os.path.exists(features_file):
                os.unlink(features_file)

            print(f"✓ {audio_file} 处理完成")

        except Exception as e:
            print(f"✗ 处理 {audio_file} 时发生错误: {e}")
            import traceback
            traceback.print_exc()

    print(f"\n=== 验证完成 ===")
    print(f"成功处理: {success_count}/{len(audio_files)} 个音频文件")

    if success_count > 0:
        print(f"\n📁 输出文件位置: {output_dir}")
        print(f"  *_original.wav - 原始音频")
        print(f"  *_denoised.wav - 降噪后音频")
        print(f"\n🎧 建议:")
        print(f"  1. 播放原始音频和降噪后音频进行对比")
        print(f"  2. 检查降噪效果是否明显")
        print(f"  3. 确认音质是否保持良好")

    return success_count > 0

def main():
    """主函数"""
    # 默认参数
    model_path = "model_output/checkpoints/rnnoise_test10_5.pth"
    test_audio_dir = "test_voice"
    output_dir = "test_output"

    print("🎯 RNNoise降噪效果验证脚本")
    print("=" * 50)

    # 验证路径
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return

    if not os.path.exists(test_audio_dir):
        print(f"❌ 测试音频目录不存在: {test_audio_dir}")
        return

    # 开始验证
    try:
        success = test_audio_denoising(model_path, test_audio_dir, output_dir)

        if success:
            print("\n🎉 验证成功完成！")
        else:
            print("\n❌ 验证失败")

    except Exception as e:
        print(f"❌ 验证过程发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
