#!/usr/bin/env python3
"""
快速RNNoise降噪测试脚本 - 直接输出WAV格式
"""

import numpy as np
import torch
import os
import struct
import wave
import rnnoise
import tempfile
import subprocess

def load_model(model_path):
    """加载RNNoise模型"""
    print(f"加载模型: {model_path}")
    
    checkpoint = torch.load(model_path, map_location='cpu')
    model_args = checkpoint.get('model_args', ())
    model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 384})
    
    model = rnnoise.RNNoise(*model_args, **model_kwargs)
    model.load_state_dict(checkpoint['state_dict'])
    model.eval()
    
    print(f"✓ 模型加载成功")
    print(f"  参数数量: {sum(p.numel() for p in model.parameters())}")
    print(f"  训练轮次: {checkpoint.get('epoch', 'Unknown')}")
    print(f"  训练损失: {checkpoint.get('loss', 'Unknown')}")
    
    return model

def extract_features_fast(wav_file):
    """快速特征提取"""
    try:
        print(f"提取特征: {wav_file}")
        
        # 创建临时特征文件
        with tempfile.NamedTemporaryFile(suffix='.f32', delete=False) as temp_features:
            temp_features_path = temp_features.name
        
        # 调用特征提取脚本
        cmd = [
            'python', 'extract_original_features.py',
            '--input', wav_file,
            '--output', temp_features_path
        ]
        
        print("  正在提取特征...")
        result = subprocess.run(cmd, capture_output=True, text=True, cwd='.', timeout=180)
        
        if result.returncode == 0:
            if os.path.exists(temp_features_path) and os.path.getsize(temp_features_path) > 0:
                print(f"  ✓ 特征提取成功")
                return temp_features_path
            else:
                print(f"  ✗ 特征文件为空")
                return None
        else:
            print(f"  ✗ 特征提取失败: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("  ✗ 特征提取超时")
        return None
    except Exception as e:
        print(f"  ✗ 特征提取错误: {e}")
        return None

def denoise_audio(model, features_file, wav_input, wav_output):
    """使用RNNoise进行降噪并直接输出WAV"""
    try:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        
        print("  开始降噪处理...")
        
        # 读取特征数据
        features_data = np.memmap(features_file, dtype='float32', mode='r')
        dim = 98
        num_frames = len(features_data) // dim
        
        if num_frames == 0:
            print("  ✗ 特征数据为空")
            return False
        
        features = features_data[:num_frames * dim].reshape(num_frames, dim)
        print(f"  特征形状: {features.shape}")
        
        # 读取原始音频
        with wave.open(wav_input, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            channels = wav.getnchannels()
            sampwidth = wav.getsampwidth()
            framerate = wav.getframerate()
        
        # 转换为float32数组
        samples = struct.unpack(f'<{len(frames)//2}h', frames)
        audio_data = np.array(samples, dtype=np.float32) / 32768.0
        
        print(f"  音频长度: {len(audio_data)} 样本")
        
        # 创建输出音频
        denoised_audio = np.copy(audio_data)
        frame_size = 480
        
        with torch.no_grad():
            # 分批处理特征
            batch_size = 2000
            num_batches = (num_frames + batch_size - 1) // batch_size
            
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, num_frames)
                
                batch_features = features[start_idx:end_idx, :65]  # 只取输入特征
                
                # 转换为tensor
                features_tensor = torch.from_numpy(batch_features).unsqueeze(0).to(device)
                
                # RNNoise前向传播
                pred_gain, pred_vad, _ = model(features_tensor)
                
                # 获取预测结果
                pred_gain = pred_gain.squeeze(0).cpu().numpy()
                pred_vad = pred_vad.squeeze(0).cpu().numpy()
                
                # 应用降噪到对应的音频段
                for frame_idx in range(len(pred_gain)):
                    global_frame_idx = start_idx + frame_idx
                    audio_start = global_frame_idx * frame_size
                    audio_end = min(audio_start + frame_size, len(audio_data))
                    
                    if audio_start < len(audio_data):
                        frame = audio_data[audio_start:audio_end]
                        gain = pred_gain[frame_idx]
                        vad_score = pred_vad[frame_idx][0]
                        
                        # 应用频域降噪
                        denoised_frame = apply_gain_to_frame(frame, gain, vad_score)
                        denoised_audio[audio_start:audio_end] = denoised_frame
        
        # 转换回int16并保存为WAV
        denoised_samples = np.clip(denoised_audio * 32768.0, -32768, 32767).astype(np.int16)
        
        with wave.open(wav_output, 'wb') as wav:
            wav.setnchannels(channels)
            wav.setsampwidth(sampwidth)
            wav.setframerate(framerate)
            wav.writeframes(denoised_samples.tobytes())
        
        print(f"  ✓ 降噪完成: {wav_output}")
        return True
        
    except Exception as e:
        print(f"  ✗ 降噪失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def apply_gain_to_frame(frame, gain, vad_score):
    """对音频帧应用增益"""
    try:
        if len(frame) == 0:
            return frame
        
        # 确保帧长度
        frame_size = 480
        if len(frame) < frame_size:
            padded_frame = np.zeros(frame_size)
            padded_frame[:len(frame)] = frame
            frame = padded_frame
        elif len(frame) > frame_size:
            frame = frame[:frame_size]
        
        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame)
        fft_phase = np.angle(fft_frame)
        
        # 应用32频带增益
        denoised_magnitude = np.copy(fft_magnitude)
        
        for band in range(32):
            start_bin = int(band * 256 / 32)
            end_bin = int((band + 1) * 256 / 32)
            
            # 获取增益并处理
            band_gain = gain[band]
            processed_gain = max(0, band_gain)
            processed_gain = processed_gain * (np.tanh(8 * processed_gain) ** 2)
            
            # 根据VAD调整
            if vad_score > 0.5:
                applied_gain = 0.7 + 0.3 * processed_gain
            else:
                applied_gain = processed_gain * 0.5
            
            applied_gain = np.clip(applied_gain, 0.1, 1.2)
            
            # 应用增益
            denoised_magnitude[start_bin:end_bin] *= applied_gain
            if start_bin > 0:
                denoised_magnitude[512-end_bin:512-start_bin] *= applied_gain
        
        # 重构信号
        denoised_fft = denoised_magnitude * np.exp(1j * fft_phase)
        denoised_frame = np.real(np.fft.ifft(denoised_fft))[:frame_size]
        denoised_frame = np.clip(denoised_frame, -1.0, 1.0)
        
        return denoised_frame[:len(frame)]
        
    except Exception as e:
        print(f"增益应用失败: {e}")
        return frame

def main():
    """主函数"""
    print("🎯 RNNoise快速降噪测试")
    print("=" * 40)
    
    # 参数设置
    model_path = "model_output/checkpoints/rnnoise_test10_5.pth"
    test_audio_dir = "test_voice"
    output_dir = "test_output"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载模型
    try:
        model = load_model(model_path)
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 获取音频文件
    audio_files = [f for f in os.listdir(test_audio_dir) if f.endswith('.wav')]
    print(f"\n找到 {len(audio_files)} 个音频文件")
    
    success_count = 0
    
    for audio_file in audio_files[:3]:  # 只处理前3个文件进行快速测试
        print(f"\n--- 处理: {audio_file} ---")
        
        wav_input = os.path.join(test_audio_dir, audio_file)
        base_name = os.path.splitext(audio_file)[0]
        wav_output = os.path.join(output_dir, f"{base_name}_denoised.wav")
        
        try:
            # 提取特征
            features_file = extract_features_fast(wav_input)
            if features_file is None:
                print(f"  ✗ 跳过 {audio_file}")
                continue
            
            # 降噪处理
            if denoise_audio(model, features_file, wav_input, wav_output):
                # 保存原始音频副本
                original_output = os.path.join(output_dir, f"{base_name}_original.wav")
                import shutil
                shutil.copy2(wav_input, original_output)
                
                print(f"  ✓ 处理完成")
                print(f"    原始: {original_output}")
                print(f"    降噪: {wav_output}")
                success_count += 1
            else:
                print(f"  ✗ 降噪失败")
            
            # 清理临时文件
            if os.path.exists(features_file):
                os.unlink(features_file)
                
        except Exception as e:
            print(f"  ✗ 处理错误: {e}")
    
    print(f"\n🎉 测试完成!")
    print(f"成功处理: {success_count}/{min(len(audio_files), 3)} 个文件")
    print(f"输出目录: {output_dir}")
    print(f"\n🎧 建议:")
    print(f"  1. 播放 *_original.wav 和 *_denoised.wav 进行对比")
    print(f"  2. 检查降噪效果和音质")

if __name__ == "__main__":
    main()
