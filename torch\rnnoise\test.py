import os
import torch
import numpy as np
import soundfile as sf
import subprocess
from rnnoise import RNNoise

# ====== 你只需修改这里的路径 ======
MODEL_PATH = "output/checkpoints/rnnoise_best.pth"   # 你的模型权重文件
INPUT_WAV = "noisy.wav"                              # 你的输入音频
OUTPUT_WAV = "denoised.wav"                          # 输出音频
FFMPEG_PATH = "ffmpeg"                               # ffmpeg可执行文件路径（如有需要可写绝对路径）
# ===================================

def convert_wav_to_16k(wav_in, wav_out, ffmpeg_path=FFMPEG_PATH):
    """
    将任意WAV音频转换为16kHz、单声道、16bit PCM的WAV格式
    """
    cmd = [
        ffmpeg_path,
        "-y",
        "-i", wav_in,
        "-ar", "16000",
        "-ac", "1",
        "-sample_fmt", "s16",
        wav_out
    ]
    print("音频格式转换命令:", " ".join(cmd))
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print("格式转换失败:", result.stderr)
        return False
    print("格式转换成功:", wav_out)
    return True

def extract_features(frame, n_fft=512):
    """
    简单的特征提取函数，生成65维输入特征（可根据训练时的特征提取方式调整）
    """
    fft_frame = np.fft.fft(frame, n=n_fft)
    fft_magnitude = np.abs(fft_frame[:n_fft // 2])
    band_energies = [np.mean(fft_magnitude[i*8:(i+1)*8]) for i in range(32)]
    log_band_energies = [np.log1p(e) for e in band_energies]
    total_energy = np.log1p(np.sum(fft_magnitude))
    features = np.array(band_energies + log_band_energies + [total_energy], dtype=np.float32)
    return features

def denoise_wav(input_wav, output_wav, model_path, frame_size=480):
    # 1. 加载模型
    checkpoint = torch.load(model_path, map_location='cpu')
    model_args = checkpoint.get('model_args', ())
    model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 384})
    model = RNNoise(*model_args, **model_kwargs)
    model.load_state_dict(checkpoint['state_dict'])
    model.eval()

    # 2. 读取音频
    audio, sr = sf.read(input_wav)
    if audio.ndim > 1:
        audio = audio[:, 0]  # 只取单声道
    audio = audio.astype(np.float32)
    num_frames = len(audio) // frame_size

    # 3. 分帧提取特征并推理
    denoised_audio = np.zeros_like(audio)
    for i in range(num_frames):
        frame = audio[i*frame_size:(i+1)*frame_size]
        if len(frame) < frame_size:
            frame = np.pad(frame, (0, frame_size - len(frame)))
        features = extract_features(frame)
        features_tensor = torch.from_numpy(features).view(1, 1, -1).float()
        with torch.no_grad():
            gain, vad, _ = model(features_tensor)
        gain = gain.squeeze().numpy()
        denoised_audio[i*frame_size:(i+1)*frame_size] = frame * gain.mean()

    # 4. 保存降噪后音频
    sf.write(output_wav, denoised_audio, sr)
    print(f"降噪完成，输出文件: {output_wav}")

if __name__ == "__main__":
    # 1. 格式转换
    temp_wav = "temp_16k.wav"
    if not convert_wav_to_16k(INPUT_WAV, temp_wav):
        exit(1)

    # 2. 推理降噪
    denoise_wav(temp_wav, OUTPUT_WAV, MODEL_PATH)

    # 3. 清理临时文件
    if os.path.exists(temp_wav):
        os.remove(temp_wav)

    print("全部流程完成！输出文件:", OUTPUT_WAV)