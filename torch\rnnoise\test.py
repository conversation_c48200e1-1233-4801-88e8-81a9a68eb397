import os
import torch
import numpy as np
import wave
import struct
import subprocess
from rnnoise import RNNoise

# ====== 你只需修改这里的路径 ======
MODEL_PATH = "RNNoise_train/model_output/checkpoints/rnnoise_test10_5.pth"   # 你的模型权重文件
INPUT_WAV = "RNNoise_train/test_voice/20250716_084323.wav"                   # 你的输入音频
OUTPUT_WAV = "torch/rnnoise/denoised.wav"                                   # 输出音频
FFMPEG_PATH = "../../env/Scripts/ffmpeg.exe"                                 # ffmpeg可执行文件路径
# ===================================

def convert_wav_to_16k(wav_in, wav_out, target_rate=16000):
    """
    将任意WAV音频转换为16kHz、单声道、16bit PCM的WAV格式（不使用ffmpeg）
    """
    try:
        print(f"音频格式转换: {wav_in} -> {wav_out}")

        # 读取原始WAV文件
        with wave.open(wav_in, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            orig_rate = wav.getframerate()
            channels = wav.getnchannels()
            sampwidth = wav.getsampwidth()

        print(f"原始格式: {orig_rate}Hz, {channels}声道, {sampwidth*8}位")

        # 转换为float32数组
        if sampwidth == 2:  # 16位
            samples = struct.unpack(f'<{len(frames)//2}h', frames)
            audio = np.array(samples, dtype=np.float32) / 32768.0
        else:
            print(f"不支持的采样位数: {sampwidth*8}位")
            return False

        # 如果是多声道，只取第一声道
        if channels > 1:
            audio = audio[::channels]

        # 简单的重采样（如果需要）
        if orig_rate != target_rate:
            # 简单的线性插值重采样
            ratio = target_rate / orig_rate
            new_length = int(len(audio) * ratio)
            indices = np.linspace(0, len(audio) - 1, new_length)
            audio = np.interp(indices, np.arange(len(audio)), audio)
            print(f"重采样: {orig_rate}Hz -> {target_rate}Hz")

        # 转换为int16并保存
        audio_int16 = np.clip(audio * 32768.0, -32768, 32767).astype(np.int16)

        with wave.open(wav_out, 'wb') as wav:
            wav.setnchannels(1)  # 单声道
            wav.setsampwidth(2)  # 16位
            wav.setframerate(target_rate)
            wav.writeframes(audio_int16.tobytes())

        print(f"格式转换成功: {wav_out}")
        print(f"输出格式: {target_rate}Hz, 1声道, 16位")
        return True

    except Exception as e:
        print(f"格式转换失败: {e}")
        return False

def extract_features(frame, n_fft=512):
    """
    简单的特征提取函数，生成65维输入特征（可根据训练时的特征提取方式调整）
    """
    fft_frame = np.fft.fft(frame, n=n_fft)
    fft_magnitude = np.abs(fft_frame[:n_fft // 2])
    band_energies = [np.mean(fft_magnitude[i*8:(i+1)*8]) for i in range(32)]
    log_band_energies = [np.log1p(e) for e in band_energies]
    total_energy = np.log1p(np.sum(fft_magnitude))
    features = np.array(band_energies + log_band_energies + [total_energy], dtype=np.float32)
    return features

def read_wav(wav_file):
    """读取WAV文件"""
    with wave.open(wav_file, 'rb') as wav:
        frames = wav.readframes(wav.getnframes())
        sample_rate = wav.getframerate()
        channels = wav.getnchannels()
        sampwidth = wav.getsampwidth()

    # 转换为float32数组
    if sampwidth == 2:  # 16位
        samples = struct.unpack(f'<{len(frames)//2}h', frames)
        audio = np.array(samples, dtype=np.float32) / 32768.0
    else:
        raise ValueError(f"不支持的采样位数: {sampwidth}")

    # 如果是多声道，只取第一声道
    if channels > 1:
        audio = audio[::channels]

    return audio, sample_rate

def write_wav(wav_file, audio, sample_rate):
    """保存WAV文件"""
    # 转换为int16
    audio_int16 = np.clip(audio * 32768.0, -32768, 32767).astype(np.int16)

    with wave.open(wav_file, 'wb') as wav:
        wav.setnchannels(1)  # 单声道
        wav.setsampwidth(2)  # 16位
        wav.setframerate(sample_rate)
        wav.writeframes(audio_int16.tobytes())

def denoise_wav(input_wav, output_wav, model_path, frame_size=480):
    # 1. 加载模型
    checkpoint = torch.load(model_path, map_location='cpu')
    model_args = checkpoint.get('model_args', ())
    model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 384})
    model = RNNoise(*model_args, **model_kwargs)
    model.load_state_dict(checkpoint['state_dict'])
    model.eval()
    print(f"✓ 模型加载成功，参数数量: {sum(p.numel() for p in model.parameters())}")

    # 2. 读取音频
    audio, sr = read_wav(input_wav)
    print(f"✓ 音频读取成功，长度: {len(audio)} 样本，采样率: {sr}Hz")
    num_frames = len(audio) // frame_size

    # 3. 分帧提取特征并推理
    denoised_audio = np.zeros_like(audio)
    print(f"开始处理 {num_frames} 帧...")

    for i in range(num_frames):
        frame = audio[i*frame_size:(i+1)*frame_size]
        if len(frame) < frame_size:
            frame = np.pad(frame, (0, frame_size - len(frame)))
        features = extract_features(frame)
        features_tensor = torch.from_numpy(features).view(1, 1, -1).float()
        with torch.no_grad():
            gain, _, _ = model(features_tensor)
        gain = gain.squeeze().numpy()
        denoised_audio[i*frame_size:(i+1)*frame_size] = frame * gain.mean()

        if (i + 1) % 100 == 0:
            print(f"  已处理 {i + 1}/{num_frames} 帧")

    # 4. 保存降噪后音频
    write_wav(output_wav, denoised_audio, sr)
    print(f"✓ 降噪完成，输出文件: {output_wav}")

if __name__ == "__main__":
    # 1. 格式转换
    temp_wav = "temp_16k.wav"
    if not convert_wav_to_16k(INPUT_WAV, temp_wav):
        exit(1)

    # 2. 推理降噪
    denoise_wav(temp_wav, OUTPUT_WAV, MODEL_PATH)

    # 3. 清理临时文件
    if os.path.exists(temp_wav):
        os.remove(temp_wav)

    print("全部流程完成！输出文件:", OUTPUT_WAV)