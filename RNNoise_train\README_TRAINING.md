# RNNoise 完整训练系统

本文件夹包含完整的RNNoise自定义模型训练系统，使用**原封不动**的RNNoise训练代码，确保训练结果的准确性和兼容性。

## 📁 文件结构

```
RNNoise_train/
├── clean_PCM_data/              # 清晰语音PCM文件
├── noise_PCM_data/              # 噪声语音PCM文件  
├── features/                    # 训练特征文件
│   └── original_training_features.f32  # 已提取的特征
├── model_output/                # 训练输出目录
│   └── checkpoints/             # 模型检查点
├── test_voice/                  # 测试音频文件
├── sparsification/              # 稀疏化模块 (原始代码)
├── rnnoise.py                   # RNNoise模型定义 (原始代码)
├── train_rnnoise.py             # 原始训练脚本 (原始代码)
├── train_rnnoise_original.py    # 训练包装脚本
├── validate_model.py            # 模型验证脚本
├── run_complete_training.py     # 完整训练流程
├── dump_rnnoise_weights.py      # 权重导出脚本 (原始代码)
├── extract_original_features.py # 特征提取脚本
└── README_TRAINING.md           # 本文件
```

## 🚀 快速开始

### 一键完整训练 (推荐)

```bash
cd RNNoise_train
python run_complete_training.py --epochs 200 --batch-size 128
```

### 分步执行

#### 步骤1: 训练模型
```bash
python train_rnnoise_original.py --epochs 200 --batch-size 128
```

#### 步骤2: 验证模型
```bash
python validate_model.py --model model_output/checkpoints/rnnoise_custom_200.pth
```

#### 步骤3: 导出C代码
```bash
python dump_rnnoise_weights.py --quantize model_output/checkpoints/rnnoise_custom_200.pth c_export
```

## 📋 核心特性

### ✅ 原始代码保证
- **完全使用原始RNNoise训练代码**，无任何修改
- 包含原始的稀疏化模块和权重导出功能
- 确保训练结果与官方版本完全兼容

### ✅ 完整验证体系
- **降噪效果验证**: 实际音频降噪测试
- **格式转换验证**: PCM/WAV格式转换测试
- **模型完整性验证**: 检查训练输出和模型参数

### ✅ 便捷使用
- 一键完整训练流程
- 自动依赖检查和错误处理
- 详细的进度显示和结果报告

## 🔧 训练参数

### 基本参数
```bash
--epochs 200              # 训练轮次
--batch-size 128          # 批次大小
--lr 1e-3                 # 学习率
--sequence-length 2000    # 序列长度
```

### 模型参数
```bash
--cond-size 128           # 条件层大小
--gru-size 384            # GRU层大小
--gamma 0.25              # 感知指数
```

### 高级选项
```bash
--sparse                  # 启用稀疏化训练
--initial-checkpoint      # 从检查点继续训练
--suffix _custom          # 模型名称后缀
```

## 📊 训练配置建议

### 快速测试 (约30分钟)
```bash
python run_complete_training.py --epochs 50 --batch-size 64
```

### 标准训练 (约2-3小时)
```bash
python run_complete_training.py --epochs 200 --batch-size 128
```

### 高质量训练 (约6-8小时)
```bash
python run_complete_training.py --epochs 500 --batch-size 128 --sparse
```

## 📈 输出文件

### 训练输出
- `model_output/checkpoints/`: 训练检查点
  - `rnnoise_custom_1.pth` ~ `rnnoise_custom_200.pth`

### 验证输出
- `test_conversion/`: 格式转换测试结果
- `denoised_output/`: 降噪效果测试结果
  - `*_original.wav`: 原始音频
  - `*_denoised.wav`: 降噪后音频

### C代码导出
- `model_output/c_export/`: C语言代码
  - `rnnoise_data.c`: 权重数据
  - `rnnoise_data.h`: 头文件

## 🎯 验证降噪效果

### 自动验证
```bash
python validate_model.py --model model_output/checkpoints/rnnoise_custom_200.pth
```

### 手动验证
1. 播放 `denoised_output/*_original.wav` (原始音频)
2. 播放 `denoised_output/*_denoised.wav` (降噪后音频)
3. 对比噪声减少效果和音质保持情况

### 验证指标
- ✅ 噪声明显减少
- ✅ 语音清晰度保持
- ✅ 无明显失真或伪影
- ✅ 格式转换无损

## 🔍 故障排除

### 常见问题

1. **"找不到特征文件"**
   ```bash
   python extract_original_features.py
   ```

2. **"CUDA内存不足"**
   ```bash
   python run_complete_training.py --batch-size 64
   ```

3. **"训练中断"**
   ```bash
   python train_rnnoise_original.py --initial-checkpoint model_output/checkpoints/rnnoise_custom_X.pth
   ```

4. **"验证失败"**
   - 检查测试音频文件是否存在
   - 确认模型文件完整性
   - 查看错误日志

### 性能优化

1. **GPU加速**: 确保CUDA可用
2. **内存优化**: 减少batch_size或sequence_length
3. **并行处理**: 增加num_workers (在train_rnnoise.py中)

## 📝 使用流程

### 完整流程
1. **准备数据**: 确保特征文件存在
2. **开始训练**: `python run_complete_training.py`
3. **验证效果**: 检查降噪结果
4. **导出模型**: 获取C代码用于部署

### 继续训练
```bash
python train_rnnoise_original.py \
  --initial-checkpoint model_output/checkpoints/rnnoise_custom_200.pth \
  --epochs 300
```

### 自定义验证
```bash
python validate_model.py \
  --model your_model.pth \
  --test-audio-dir your_test_audio/
```

## 🎉 成功标志

训练成功完成后，您应该看到:

1. ✅ 模型检查点文件生成
2. ✅ 验证测试通过
3. ✅ 降噪效果明显
4. ✅ C代码成功导出

## 📞 技术支持

如果遇到问题:
1. 检查依赖项是否正确安装
2. 确认输入数据格式正确
3. 查看详细错误日志
4. 尝试减少训练参数重新运行

---

**注意**: 本训练系统完全基于原始RNNoise代码，确保了最高的兼容性和准确性。
